﻿using Builder.ProjectServicesSecurity.API.Domain.Enum;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ;
using Builder.ProjectServicesSecurity.API.Persistence.Entities;
using Builder.ProjectServicesSecurity.API.Persistence;
using DWH.ProjectServices.API.IntegrationTests.MockService;
using Moq;
using Xunit;
using Builder.ProjectServicesSecurity.API.Services.Models;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace Builder.ProjectServicesSecurity.API.IntegrationTests.MockService
{
    public class OutBoxBackgroundServiceIntegrationTests : IClassFixture<ProjectServicesAPIServer>, IClassFixture<APIFactory>
    {
        private readonly OutBoxBackgroundService _backgroundService;
        private readonly PostgreSqlDbContext _dbContext;
        private readonly Mock<IRabbitMQSender> _mockRabbitMqSender;

        public OutBoxBackgroundServiceIntegrationTests(ProjectServicesAPIServer mockServer, APIFactory factory)
        {
            var scope = factory.Services.CreateScope();
            _dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            _mockRabbitMqSender = new Mock<IRabbitMQSender>();

            _backgroundService = new OutBoxBackgroundService(
                factory.Services.GetRequiredService<IServiceScopeFactory>(),
                Mock.Of<ILogger<OutBoxBackgroundService>>(),
                _mockRabbitMqSender.Object);
        }

        [Fact]
        public async Task ExecuteAsync_ShouldProcessOutboxMessages()
        {


            // Arrange
            var messageId = Guid.NewGuid();
            var testMessage = new OutBoxItemEntity
            {
                Id = messageId,
                Payload = JsonSerializer.Serialize(new { Id = messageId, SyncingEntityId = "111-123" }),
                TypeId = OutboxMessageTypes.BPSecurityCreate,
                Status = OutboxStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.OutBoxItem.Add(testMessage);
            await _dbContext.SaveChangesAsync();

            using var cts = new CancellationTokenSource();

            // Act
            await _backgroundService.StartAsync(cts.Token);
            await Task.Delay(20000);


            var processedMessage = await _dbContext.OutBoxItem
            .AsNoTracking() 
            .FirstOrDefaultAsync(m => m.Id == messageId);

            Assert.NotNull(processedMessage);
            processedMessage.Status.Should().Be(OutboxStatus.Processed);

            // Assert
            _mockRabbitMqSender.Verify(sender =>
                sender.SendToRabbitMQ(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ProjectServicesData>()),
                Times.AtLeastOnce);

            
        }
    }
}
