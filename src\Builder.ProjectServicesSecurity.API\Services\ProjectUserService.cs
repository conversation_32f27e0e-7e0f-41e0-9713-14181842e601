using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Persistence.Entities;
using Builder.ProjectServicesSecurity.API.Persistence.Repositories;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Models;
using Builder.ProjectServicesSecurity.Services.Constants;
using ProjectType = Builder.ProjectServicesSecurity.API.Domain.ProjectType;

namespace Builder.ProjectServicesSecurity.API.Services
{
    public class ProjectUserService : IProjectUserService
    {
        private readonly ILogger<ProjectUserService> _logger;
        private readonly IProjectUserRepository _projectUserRepository;
        private readonly IWebAPIService _webAPIService;
        private readonly IOutBoxItemRepository _outBoxItemRepository;

        public ProjectUserService(IProjectUserRepository projectUserRepository, ILogger<ProjectUserService> logger, IOutBoxItemRepository outBoxItemRepository, IWebAPIService webAPIService)
        {
            _projectUserRepository = projectUserRepository;
            _logger = logger;
            _webAPIService = webAPIService;
            _outBoxItemRepository = outBoxItemRepository;
        }

        public async Task<Tuple<List<Project>, List<Project>>> AddAsync(ProjectDetails projectDetails)
        {
            var masterUserIds = new List<int>();
            var validProjectIds = await FilterProjectIdsBasedOnCountryIds(
                projectDetails.CountryIds,
                projectDetails.Projects.Select(p => p.ProjectId).ToList(),
                projectDetails.ProjectTypeId
            );

            if (validProjectIds == null || !validProjectIds.Any())
                throw new HttpRequestException("AddAsync - No Valid Project Found");

            var filteredProjects = projectDetails.Projects.Where(p => validProjectIds.Contains(p.ProjectId)).ToList();
            _logger.LogDebug("ProjectServicesSecurity - AddAsync :: Username: {username}", projectDetails.Username);

            if (projectDetails.ProjectTypeId == (int)ProjectType.BPSecurity)
            {
                masterUserIds = await _webAPIService.GetMasterUsers();
                var tupleResult = FilterMasterUsers(projectDetails, masterUserIds);

                if (tupleResult.Item1.Count > 0 && tupleResult.Item2.Count == 0)
                {
                    return new Tuple<List<Project>, List<Project>>(new List<Project>(), new List<Project>());
                }
            }

            var projectDetailRepo = new ProjectDetails
            {
                Projects = filteredProjects,
                Username = projectDetails.Username,
                ProjectTypeId = projectDetails.ProjectTypeId
            };

            var result = await _projectUserRepository.AddAsync(projectDetailRepo, masterUserIds);

            if (result == null || (!result.Item1.Any() && !result.Item2.Any()))
            {
                _logger.LogError("AddAsync - error while assigning users");
                throw new ArgumentNullException(nameof(AddAsync), "Error while assigning users");
            }

            await SendMessagesToOutbox(result.Item1, projectDetails.ProjectTypeId, "Create");
            return result;
        }

        public async Task<List<int>> FilterProjectIdsBasedOnCountryIds(string countryIds, IEnumerable<int> projectIds, int projectTypeId)
        {
            _logger.LogDebug("Entered in FilterProjectIdsBasedOnCountryIds method - countryIds :: {countryIds} - projectIds :: {projectIds} - projectTypeId {projectTypeId}"
                , countryIds, string.Join(", ", projectIds), projectTypeId);

            if (countryIds == null)
            {
                return projectIds.ToList();
            }

            if (projectTypeId == (int)ProjectType.QCSecurity)
            {
                _logger.LogDebug("QC Security Case");
                var countryIdArray = countryIds.Split(',').Select(int.Parse).ToArray();
                var qcProjectRequest = new QCProjectCountry
                {
                    CountryId = countryIdArray,
                    QCProjectId = projectIds.ToArray()
                };
                var validQCProjectIds = await _webAPIService.GetQCProject(qcProjectRequest);
                return validQCProjectIds;
            }
            else
            {
                _logger.LogDebug("BP Security Case");
                var countryIdArray = countryIds.Split(',').Select(int.Parse).ToArray();
                var bpProjectRequest = new BaseProjectCountry
                {
                    CountryId = countryIdArray,
                    BaseProjectId = projectIds.ToArray()
                };

                var validBaseProjectIds = await _webAPIService.GetBaseProject(bpProjectRequest);
                return validBaseProjectIds;
            }
        }

        public async Task<List<UserHistory>> GetAsync(int projectId, int projectTypeId)
        {
            _logger.LogDebug("ProjectServicesSecurity - GetAsync :: projectId: {projectId}", projectId);
            var projectUsers = await _projectUserRepository.GetAsync(projectId);
            if (projectTypeId == (int)ProjectType.BPSecurity)
            {
                var masterUserIds = await _webAPIService.GetMasterUsers();
                await _projectUserRepository.DeleteMasterUsersFromBX(masterUserIds);
                var masterUserHistory = masterUserIds.Select(user => new UserHistory
                {
                    UserId = user,
                    CreatedBy = AppConstants.System,
                    CreatedWhen = null
                });
                projectUsers = projectUsers.Concat(masterUserHistory).DistinctBy(s => s.UserId).ToList();
            }
            return projectUsers.Count > 0 ? projectUsers : null;
        }

        public async Task<List<UserHistory>> GetAsyncList(Project project)
        {
            _logger.LogDebug("ProjectServicesSecurity - GetAsyncList :: projectId: {projectId}", project.ProjectId);
            var users = await _projectUserRepository.GetAsyncList(project);
            if (users == null)
            {
                _logger.LogError("GetAsyncList - project user not found: {project}", project.ProjectId);
                throw new ArgumentNullException("GetAsyncList", "GetAsyncList - users not found");
            }
            return users.Count > 0 ? users : null;
        }

        public async Task<Tuple<List<int>, List<int>>> DeleteAsync(ProjectDetails projectDetails)
        {
            _logger.LogDebug(">>> Entered in DeleteAsync Service method <<<");

            var validProjectIds = await FilterProjectIdsBasedOnCountryIds(projectDetails.CountryIds, new int[] { projectDetails.Projects[0].ProjectId }, projectDetails.ProjectTypeId);
            if (validProjectIds == null) throw new HttpRequestException("DeleteAsync - No Valid Project Found");
            _logger.LogDebug("ProjectServicesSecurity - DeleteAsync :: Username: {userName}", projectDetails.Username);

            var masterUserIds = await _webAPIService.GetMasterUsers();
            var tupleResult = FilterMasterUsers(projectDetails, masterUserIds);
            Project project = FilterUsers(projectDetails.Projects[0], tupleResult.Item1);

            _logger.LogDebug("Master Users Count :: {masterUsers}", tupleResult.Item1);
            _logger.LogDebug("Non-Master Users Count :: {nonMasterUsers}", tupleResult.Item2);

            if (tupleResult.Item2.Count == 0)
            {
                return tupleResult;
            }

            var result = await _projectUserRepository.DeleteAsync(project, projectDetails.Username);
            if (result == null)
            {
                _logger.LogError("DeleteAsync - project user not found: {project}", project.ProjectId);
                throw new ArgumentNullException("DeleteAsync", "DeleteAsync - project user not found");
            }

            await SendMessagesToOutbox(new List<Project> { result }, projectDetails.ProjectTypeId, "Delete");
            return tupleResult;
        }

        public async Task<Tuple<List<Project>, List<Project>>> BulkCopyAsync(BulkCopyProjectUserRequest request, string userName, string countryIds)
        {
            _logger.LogDebug("ProjectServicesSecurity - BulkCopyAsync :: Username: {username}", userName);

            if (request.ProjectTypeId != (int)ProjectType.BPSecurity)
            {
                throw new ArgumentException("Bulk copy is only supported for BP Security projects");
            }

            var allDistinctUsers = new HashSet<int>();
            var sourceProjectIds = request.ProjectMappings.Select(m => m.SourceProjectId).ToList();

            // Validate source projects exist and user has access
            var validSourceProjectIds = await FilterProjectIdsBasedOnCountryIds(
                countryIds,
                sourceProjectIds,
                request.ProjectTypeId
            );

            if (validSourceProjectIds == null || !validSourceProjectIds.Any())
                throw new HttpRequestException("BulkCopyAsync - No Valid Source Projects Found");

            // Get all distinct BP security users from source projects
            foreach (var sourceProjectId in validSourceProjectIds)
            {
                var sourceUsers = await GetAsync(sourceProjectId, request.ProjectTypeId);
                if (sourceUsers != null)
                {
                    foreach (var user in sourceUsers)
                    {
                        allDistinctUsers.Add(user.UserId);
                    }
                }
            }

            if (!allDistinctUsers.Any())
            {
                _logger.LogWarning("BulkCopyAsync - No users found in source projects");
                return new Tuple<List<Project>, List<Project>>(new List<Project>(), new List<Project>());
            }

            // Create projects for new base projects with all distinct users
            var projects = new List<Project>();
            var newBaseProjectIds = request.ProjectMappings.Select(m => m.NewBaseProjectId).ToList();

            // Validate new base projects exist and user has access
            var validNewBaseProjectIds = await FilterProjectIdsBasedOnCountryIds(
                countryIds,
                newBaseProjectIds,
                request.ProjectTypeId
            );

            if (validNewBaseProjectIds == null || !validNewBaseProjectIds.Any())
                throw new HttpRequestException("BulkCopyAsync - No Valid New Base Projects Found");

            foreach (var newBaseProjectId in validNewBaseProjectIds)
            {
                var userHistory = allDistinctUsers.Select(userId => new UserHistory
                {
                    UserId = userId
                }).ToList();

                var project = new Project(newBaseProjectId, userHistory);
                projects.Add(project);
            }

            // Use existing AddAsync logic to add users to new base projects
            var projectDetails = new ProjectDetails
            {
                Projects = projects,
                Username = userName,
                CountryIds = countryIds,
                ProjectTypeId = request.ProjectTypeId
            };

            return await AddAsync(projectDetails);
        }

        private Project FilterUsers(Project project, List<int> masterUsers)
        {
            _logger.LogDebug("Entered in FilterUsers method");
            _logger.LogDebug("Master Users :: {masterUsers}", string.Join(", ", masterUsers));

            project.Users.RemoveAll(user => masterUsers.Contains(user.UserId));
            return project;
        }

        private Tuple<List<int>, List<int>> FilterMasterUsers(ProjectDetails projectDetails, List<int> masterUserIds)
        {
            _logger.LogDebug("Entered in FilterMasterUsers method");

            var masterUsers = new List<int>();
            var nonMasterUsers = new List<int>();

            if (projectDetails.ProjectTypeId == (int)Domain.ProjectType.BPSecurity)
            {
                foreach (var project in projectDetails.Projects)
                {
                    var userIds = project.Users.Select(c => c.UserId).ToList();
                    masterUsers = masterUserIds.Intersect(userIds).ToList();
                    nonMasterUsers = userIds.Except(masterUserIds).ToList();
                }
            }
            else if (projectDetails.ProjectTypeId == (int)ProjectType.QCSecurity)
            {
                var userIds = projectDetails.Projects[0].Users.Select(c => c.UserId).ToList();

                if (projectDetails.CountryIds != null)
                {
                    masterUsers = masterUserIds.Intersect(userIds).ToList();
                    nonMasterUsers = userIds.Except(masterUserIds).ToList();
                }
                else
                {
                    masterUsers = new List<int>();
                    nonMasterUsers = userIds;
                }
            }

            return new Tuple<List<int>, List<int>>(masterUsers, nonMasterUsers);
        }


        private async Task SendMessagesToOutbox(List<Project> projects, int projectTypeId, string action)
        {
            foreach (var project in projects)
            {
                try
                {
                    string exchangeName = projectTypeId == (int)ProjectType.QCSecurity ?
                        ProjectSecurityConstants.QCSecurityExchange :
                        ProjectSecurityConstants.BPSecurityExchange;

                    string messageType = projectTypeId == (int)ProjectType.QCSecurity ?
                        $"QCSecurity{action}" :
                        $"BPSecurity{action}";

                    var userIds = string.Join(",", project.Users.Select(u => u.UserId));
                    var syncingEntityId = $"{project.ProjectId}-{userIds}";


                    await _outBoxItemRepository.SaveMessagesAsync(
                        new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = syncingEntityId },
                        messageType
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogError("Error sending message to Outbox: {error}", ex);
                }
            }
        }
    }
}

