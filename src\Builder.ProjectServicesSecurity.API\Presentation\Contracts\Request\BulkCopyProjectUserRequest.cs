using System.ComponentModel.DataAnnotations;

namespace Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request
{
    public class BulkCopyProjectUserRequest : IValidatableObject
    {
        public int ProjectTypeId { get; set; }
        public List<ProjectMapping> ProjectMappings { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();
            
            if (ProjectMappings == null || ProjectMappings.Count <= 0)
            {
                results.Add(new ValidationResult("Must have at least one project mapping to proceed"));
            }

            if (ProjectTypeId != 1 && ProjectTypeId != 2)
            {
                results.Add(new ValidationResult("Please provide valid projectTypeId"));
            }

            if (ProjectMappings != null)
            {
                for (int i = 0; i < ProjectMappings.Count; i++)
                {
                    var mapping = ProjectMappings[i];
                    if (mapping.SourceProjectId <= 0)
                    {
                        results.Add(new ValidationResult($"ProjectMappings[{i}].SourceProjectId must be greater than 0"));
                    }
                    if (mapping.NewBaseProjectId <= 0)
                    {
                        results.Add(new ValidationResult($"ProjectMappings[{i}].NewBaseProjectId must be greater than 0"));
                    }
                }

                // Check for duplicate source projects
                var duplicateSourceProjects = ProjectMappings
                    .GroupBy(x => x.SourceProjectId)
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key)
                    .ToList();

                if (duplicateSourceProjects.Any())
                {
                    results.Add(new ValidationResult($"Duplicate source project IDs found: {string.Join(", ", duplicateSourceProjects)}"));
                }

                // Check for duplicate new base projects
                var duplicateNewBaseProjects = ProjectMappings
                    .GroupBy(x => x.NewBaseProjectId)
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key)
                    .ToList();

                if (duplicateNewBaseProjects.Any())
                {
                    results.Add(new ValidationResult($"Duplicate new base project IDs found: {string.Join(", ", duplicateNewBaseProjects)}"));
                }
            }

            return results;
        }
    }

    public class ProjectMapping
    {
        public int SourceProjectId { get; set; }
        public int NewBaseProjectId { get; set; }
    }
}
