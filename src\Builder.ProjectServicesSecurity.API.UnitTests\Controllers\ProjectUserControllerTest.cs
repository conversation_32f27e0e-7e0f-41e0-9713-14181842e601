﻿using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response;
using Builder.ProjectServicesSecurity.API.Presentation.Controllers;
using Builder.ProjectServicesSecurity.API.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace Builder.ProjectServicesSecurity.API.UnitTests.Controllers
{
    public class ProjectUserControllerTest
    {
        public Mock<IProjectUserService> _projectUserServiceMock { get; set; }
        public ProjectUserController _projectUserController { get; set; }

        public ProjectUserControllerTest()
        {
            _projectUserServiceMock = new Mock<IProjectUserService>();
            _projectUserController = new ProjectUserController(_projectUserServiceMock.Object);
        }

        [Fact]
        public async Task AddAsync_ShouldReturn207MultiStatus_WhenAllUsersAssignedSuccessfully()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var projectUserRequest = new ProjectUserRequest
            {
                ProjectIds = new List<int> { 1 },
                UserIds = new List<int> { 1, 2 },
                ProjectTypeId = 1
            };
            var result = new Tuple<List<Project>, List<Project>>(new List<Project>() { new Project(1, 
                new List<UserHistory> { 
                    new UserHistory()
                        {
                            UserId = 1
                        }, 
                    new UserHistory()
                        {
                            UserId = 2
                        }
                }) },
                new List<Project>());

            _projectUserServiceMock.Setup(s => s.AddAsync(It.IsAny<ProjectDetails>())).ReturnsAsync(result);

            // Act
            var response = await _projectUserController.AddAsync(userName,countryId, projectUserRequest);

            // Assert
            var objectResult = response as ObjectResult;
            objectResult.Should().NotBeNull();
            objectResult.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var projectUserResponse = objectResult.Value as ProjectUserResponse;
            projectUserResponse.ProjectResponses.Should().ContainSingle(pr =>
                pr.ProjectId == 1 &&
                pr.StatusCode == 200 &&
                pr.StatusMsg == "All users assigned successfully");
        }

        [Fact]
        public async Task AddAsync_ShouldReturn207MultiStatus_WhenNewUsersAssignedAndDuplicateUsersSkipped()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var projectUserRequest = new ProjectUserRequest
            {
                ProjectIds = new List<int> { 1 },
                UserIds = new List<int> { 1, 2 },
                ProjectTypeId = 1
            };
            var result = new Tuple<List<Project>, List<Project>>(new List<Project>() { new Project(1, new List<UserHistory> { 
                new UserHistory() {UserId = 1}
            }) },
                new List<Project>() { new Project(1, new List<UserHistory> { new UserHistory()}) });


            _projectUserServiceMock.Setup(s => s.AddAsync(It.IsAny<ProjectDetails>())).ReturnsAsync(result);

            // Act
            var response = await _projectUserController.AddAsync(userName,countryId, projectUserRequest);

            // Assert
            var objectResult = response as ObjectResult;
            objectResult.Should().NotBeNull();
            objectResult.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var projectUserResponse = objectResult.Value as ProjectUserResponse;
            projectUserResponse.ProjectResponses.Should().ContainSingle(pr =>
                pr.ProjectId == 1 &&
                pr.StatusCode == 200 &&
                pr.StatusMsg == "New users assigned successfully and duplicate/master users skipped");
        }

        [Fact]
        public async Task AddAsync_ShouldReturn207MultiStatus_WhenUsersAlreadyAssignedToProject()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var projectUserRequest = new ProjectUserRequest
            {
                ProjectIds = new List<int> { 1 },
                UserIds = new List<int> { 1, 2 },
                ProjectTypeId = 1
            };
            var result = new Tuple<List<Project>, List<Project>>(new List<Project>(),
                new List<Project>() { new Project(1, new List<UserHistory> {
                new UserHistory() { UserId = 1 },
                new UserHistory() { UserId = 2 }
                }) });

            _projectUserServiceMock.Setup(s => s.AddAsync(It.IsAny<ProjectDetails>())).ReturnsAsync(result);

            // Act
            var response = await _projectUserController.AddAsync(userName,countryId, projectUserRequest);

            // Assert
            var objectResult = response as ObjectResult;
            objectResult.Should().NotBeNull();
            objectResult.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var projectUserResponse = objectResult.Value as ProjectUserResponse;
            projectUserResponse.ProjectResponses.Should().ContainSingle(pr =>
                pr.ProjectId == 1 &&
                pr.StatusCode == 409 &&
                pr.StatusMsg == "User(s) already assigned to this project");
        }

        [Fact]
        public async Task AddAsync_ShouldReturnBadRequest_WhenResponseIsNull()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var projectUserRequest = new ProjectUserRequest
            {
                UserIds = new List<int> { 111, 112 },
                ProjectIds = new List<int> { 101 },
                ProjectTypeId = 1
            };

            var projectDetails = new ProjectDetails
            {
                Projects = It.IsAny<List<Project>>(),
                Username = userName,
                CountryIds = countryId,
                ProjectTypeId = projectUserRequest.ProjectTypeId
            };

            _projectUserServiceMock
                .Setup(s => s.AddAsync(projectDetails))
                .ReturnsAsync(new Tuple<List<Project>, List<Project>>(new List<Project>(), new List<Project>()));

            // Act
            var result = await _projectUserController.AddAsync(userName,countryId, projectUserRequest) as BadRequestObjectResult;

            // Assert
            result.Should().BeNull();
        }


        [Fact]
        public async Task GetUsers_ShouldReturn200OK_WhenUsersFound()
        {
            // Arrange
            var userIds = new List<UserHistory> {
                new UserHistory() { UserId = 1, CreatedBy = "testUser1", CreatedWhen = DateTimeOffset.UtcNow },
                new UserHistory() { UserId = 2, CreatedBy = "testUser2", CreatedWhen = DateTimeOffset.UtcNow } };
            _projectUserServiceMock.Setup(s => s.GetAsync(It.IsAny<int>(), (int)ProjectType.BPSecurity)).ReturnsAsync(userIds);

            // Act
            var response = await _projectUserController.GetUsers(It.IsAny<int>(), (int)ProjectType.BPSecurity);

            // Assert
            var okResult = response.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(StatusCodes.Status200OK);
            var userResponse = okResult.Value as UserResponse;
            userResponse.Should().NotBeNull();
            userResponse.Users.Count.Should().Be(userIds.Count);
        }

        [Fact]
        public async Task GetUsers_ShouldReturn404NotFound_WhenNoUsersFound()
        {
            // Arrange
            _projectUserServiceMock.Setup(s => s.GetAsync(It.IsAny<int>(), (int)ProjectType.BPSecurity)).ReturnsAsync((List<UserHistory>)null);

            // Act
            var response = await _projectUserController.GetUsers(It.IsAny<int>(), (int)ProjectType.BPSecurity);

            // Assert
            var notFoundResult = response.Result as NotFoundResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(StatusCodes.Status404NotFound);
        }

        [Fact]
        public async Task RemoveUsers_ShouldReturn207MultiStatus_WhenAllUsersRemovedSuccessfully()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var removeUsersRequest = new UsersRequest
            {
                ProjectId = 1,
                UserIds = new List<int> { 111, 222, 333, 444 }
            };

            var result = new Tuple<List<int>, List<int>>(new List<int>(), new List<int>() { 999, 888 });

            _projectUserServiceMock.Setup(s => s.DeleteAsync(It.IsAny<ProjectDetails>())).ReturnsAsync(result);


            // Act
            var response = await _projectUserController.RemoveUsers(userName, countryId, removeUsersRequest);

            // Assert
            var multiStatusResult = Assert.IsType<ObjectResult>(response);
            multiStatusResult.Should().NotBeNull();
            multiStatusResult.StatusCode.Should().Be(StatusCodes.Status207MultiStatus, "Status code should be 207");
            var responseInfo = multiStatusResult.Value as ProjectUserResponse;
            responseInfo.Should().NotBeNull();
            responseInfo.ProjectResponses[0].ProjectId.Should().Be(1);
            responseInfo.ProjectResponses[0].StatusCode.Should().Be(StatusCodes.Status200OK);
            responseInfo.ProjectResponses[0].StatusMsg.Should().Be("All Users Successfully Removed");
        }

        [Fact]
        public async Task RemoveUsers_ShouldReturn207MultiStatus_WhenUsersRemovedSuccessfullyWithMasterUsers()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var removeUsersRequest = new UsersRequest
            {
                ProjectId = 1,
                UserIds = new List<int> { 111, 222, 333, 444 }
            };

            var result = new Tuple<List<int>, List<int>>(new List<int>() { 111, 222 }, new List<int>() { 888, 999 });

            _projectUserServiceMock.Setup(s => s.DeleteAsync(It.IsAny<ProjectDetails>())).ReturnsAsync(result);
          

            // Act
            var response = await _projectUserController.RemoveUsers(userName,countryId, removeUsersRequest);

            // Assert
            var multiStatusResult = Assert.IsType<ObjectResult>(response);
            multiStatusResult.Should().NotBeNull();
            multiStatusResult.StatusCode.Should().Be(StatusCodes.Status207MultiStatus, "Status code should be 207");
            var responseInfo = multiStatusResult.Value as ProjectUserResponse;
            responseInfo.Should().NotBeNull();
            responseInfo.ProjectResponses[0].ProjectId.Should().Be(1);
            responseInfo.ProjectResponses[0].StatusCode.Should().Be(StatusCodes.Status200OK);
            responseInfo.ProjectResponses[0].StatusMsg.Should().Be("Following User IDs belong to master users and skipped from removal: 111, 222");
        }


        [Fact]
        public async Task RemoveUsers_ShouldReturn500InternalServerError_WhenExceptionThrown()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var removeUsersRequest = new UsersRequest
            {
                ProjectId = 1,
                UserIds = new List<int> { 1, 2 }
            };

            _projectUserServiceMock.Setup(s => s.DeleteAsync(It.IsAny<ProjectDetails>())).ThrowsAsync(new System.Exception());
           
            // Act
            var response = await _projectUserController.RemoveUsers(userName,countryId, removeUsersRequest);

            // Assert
            var objectResult = response as ObjectResult;
            objectResult.Should().NotBeNull();
            objectResult.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
        }

        [Fact]
        public async Task GetUserDetails_ReturnsOkResult_WithValidData()
        {
            // Arrange
            var usersRequest = new UsersRequest
            {
                ProjectId = 1,
                UserIds = new List<int> { 1, 2, 3 }
            };

            var userHistory = usersRequest.UserIds.Select(userId => new UserHistory
            {
                UserId = userId
            }).ToList();

            var project = new Project(usersRequest.ProjectId, userHistory);

            var userResponse = new List<UserHistory>
                                {
                                    new UserHistory { UserId = 1, CreatedBy = "Admin", CreatedWhen = DateTime.Now, DeletedBy = null, DeletedOn = null },
                                    new UserHistory { UserId = 2, CreatedBy = "Admin", CreatedWhen = DateTime.Now, DeletedBy = null, DeletedOn = null }
                                };

            _projectUserServiceMock.Setup(service => service.GetAsyncList(It.IsAny<Project>())).ReturnsAsync(userResponse);

            // Act
            var result = await _projectUserController.GetUserDetails(usersRequest);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var response = okResult.Value as UserResponse;
            response.Should().NotBeNull();
            response.ProjectId.Should().Be(usersRequest.ProjectId);
            response.Users.Should().HaveCount(2);
        }

        [Fact]
        public async Task AddAsync_ShouldReturnForbidden_WhenNoMatchingBaseProjectWithCountryFound()
        {
            // Arrange
            var userName = "TestUser";
            string countryId = "15";
            var projectUserRequest = new ProjectUserRequest
            {
                ProjectIds = new List<int> { 1 },
                UserIds = new List<int> { 1, 2 }
            };

            _projectUserServiceMock
            .Setup(s => s.AddAsync(It.IsAny<ProjectDetails>()))
            .ThrowsAsync(new HttpRequestException("AddAsync - No Valid Project Found"));

            // Act
            var result = await _projectUserController.AddAsync(userName, countryId, projectUserRequest);
            // Assert
            result.Should().BeOfType<StatusCodeResult>()
               .Which.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
        }

        [Fact]
        public async Task DeleteAsync_ShouldReturnForbidden_WhenNoMatchingBaseProjectWithCountryFound()
        {
            // Arrange
            var userName = "TestUser";
            string countryId = "15";
            var userRequest = new UsersRequest { ProjectId = 1, UserIds = new List<int> { 1 } };
            _projectUserServiceMock
            .Setup(s => s.DeleteAsync(It.IsAny<ProjectDetails>()))
            .ThrowsAsync(new HttpRequestException("AddAsync - No Valid QC Project Found"));
            // Act
            var result = await _projectUserController.RemoveUsers(userName, countryId, userRequest);
            // Assert
            result.Should().BeOfType<StatusCodeResult>()
               .Which.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
        }

        [Fact]
        public async Task GetUserDetails_ReturnsNotFound_WhenNoData()
        {
            // Arrange
            var usersRequest = new UsersRequest
            {
                ProjectId = 1,
                UserIds = new List<int> { 1, 2, 3 }
            };

            _projectUserServiceMock.Setup(service => service.GetAsyncList(It.IsAny<Project>())).ReturnsAsync((List<UserHistory>)null);

            // Act
            var result = await _projectUserController.GetUserDetails(usersRequest);

            // Assert
            var notFoundResult = result.Result as NotFoundResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
        }

        [Fact]
        public async Task BulkCopyAsync_ShouldReturn207MultiStatus_WhenUsersSuccessfullyCopied()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var bulkCopyRequest = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 1, // BP Security
                ProjectMappings = new List<ProjectMapping>
                {
                    new ProjectMapping { SourceProjectId = 100, NewBaseProjectId = 200 },
                    new ProjectMapping { SourceProjectId = 101, NewBaseProjectId = 201 }
                }
            };

            var result = new Tuple<List<Project>, List<Project>>(
                new List<Project>
                {
                    new Project(200, new List<UserHistory> { new UserHistory { UserId = 1 }, new UserHistory { UserId = 2 } }),
                    new Project(201, new List<UserHistory> { new UserHistory { UserId = 1 }, new UserHistory { UserId = 2 } })
                },
                new List<Project>()
            );

            _projectUserServiceMock.Setup(s => s.BulkCopyAsync(It.IsAny<BulkCopyProjectUserRequest>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(result);

            // Act
            var response = await _projectUserController.BulkCopyAsync(userName, countryId, bulkCopyRequest);

            // Assert
            var objectResult = response as ObjectResult;
            objectResult.Should().NotBeNull();
            objectResult.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var projectUserResponse = objectResult.Value as ProjectUserResponse;
            projectUserResponse.Should().NotBeNull();
            projectUserResponse.ProjectResponses.Should().HaveCount(2);
            projectUserResponse.ProjectResponses.Should().AllSatisfy(pr =>
            {
                pr.StatusCode.Should().Be(200);
                pr.StatusMsg.Should().Be("All users assigned successfully");
            });
        }

        [Fact]
        public async Task BulkCopyAsync_ShouldReturnBadRequest_WhenArgumentExceptionThrown()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var bulkCopyRequest = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 2, // QC Security - not supported
                ProjectMappings = new List<ProjectMapping>
                {
                    new ProjectMapping { SourceProjectId = 100, NewBaseProjectId = 200 }
                }
            };

            _projectUserServiceMock.Setup(s => s.BulkCopyAsync(It.IsAny<BulkCopyProjectUserRequest>(), It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new ArgumentException("Bulk copy is only supported for BP Security projects"));

            // Act
            var response = await _projectUserController.BulkCopyAsync(userName, countryId, bulkCopyRequest);

            // Assert
            var badRequestResult = response as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            badRequestResult.Value.Should().Be("Bulk copy is only supported for BP Security projects");
        }

        [Fact]
        public async Task BulkCopyAsync_ShouldReturnForbidden_WhenHttpRequestExceptionThrown()
        {
            // Arrange
            var userName = "testUser";
            var countryId = "15";
            var bulkCopyRequest = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 1,
                ProjectMappings = new List<ProjectMapping>
                {
                    new ProjectMapping { SourceProjectId = 100, NewBaseProjectId = 200 }
                }
            };

            _projectUserServiceMock.Setup(s => s.BulkCopyAsync(It.IsAny<BulkCopyProjectUserRequest>(), It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new HttpRequestException("BulkCopyAsync - No Valid Source Projects Found"));

            // Act
            var response = await _projectUserController.BulkCopyAsync(userName, countryId, bulkCopyRequest);

            // Assert
            var forbiddenResult = response as StatusCodeResult;
            forbiddenResult.Should().NotBeNull();
            forbiddenResult.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
        }
    }
}
