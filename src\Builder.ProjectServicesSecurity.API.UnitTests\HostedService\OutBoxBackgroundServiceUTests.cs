﻿using Builder.ProjectServicesSecurity.API.Domain.Enum;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Persistence.Entities;
using Builder.ProjectServicesSecurity.API.Persistence.Repositories;
using Builder.ProjectServicesSecurity.API.Services;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.API.Services.Models;
using Builder.ProjectServicesSecurity.API.Services.RabbitMQ.Constants;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Builder.ProjectServicesSecurity.API.UnitTests.HostedService
{
    public class OutBoxBackgroundServiceUTests
    {
        private readonly Mock<IServiceScopeFactory> _mockServiceScopeFactory;
        private readonly Mock<IServiceScope> _mockServiceScope;
        private readonly Mock<IServiceProvider> _mockServiceProvider;
        private readonly Mock<IOutBoxItemRepository> _mockOutBoxRepository;
        private readonly Mock<IRabbitMQSender> _mockRabbitMQSender;
        private readonly Mock<ILogger<OutBoxBackgroundService>> _mockLogger;
        private readonly OutBoxBackgroundService _backgroundService;

        public OutBoxBackgroundServiceUTests()
        {
            _mockServiceScopeFactory = new Mock<IServiceScopeFactory>();
            _mockServiceScope = new Mock<IServiceScope>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockOutBoxRepository = new Mock<IOutBoxItemRepository>();
            _mockRabbitMQSender = new Mock<IRabbitMQSender>();
            _mockLogger = new Mock<ILogger<OutBoxBackgroundService>>();

            _mockServiceScope.Setup(x => x.ServiceProvider).Returns(_mockServiceProvider.Object);
            _mockServiceScopeFactory.Setup(x => x.CreateScope()).Returns(_mockServiceScope.Object);

            _mockServiceProvider.Setup(x => x.GetService(typeof(IOutBoxItemRepository)))
                .Returns(_mockOutBoxRepository.Object);
            _mockServiceProvider.Setup(x => x.GetService(typeof(IRabbitMQSender)))
                .Returns(_mockRabbitMQSender.Object);

            _backgroundService = new OutBoxBackgroundService(
                _mockServiceScopeFactory.Object,
                _mockLogger.Object,
                _mockRabbitMQSender.Object
            );
        }

        [Fact]
        public async Task ExecuteAsync_ShouldRetrieveUnprocessedMessages()
        {
            // Arrange
            var messages = new List<OutBoxItemEntity>
            {
                new OutBoxItemEntity { Id = Guid.NewGuid(), TypeId = "BPSecurityCreate", Status = OutboxStatus.Pending }
            };

            _mockOutBoxRepository.Setup(repo => repo.GetUnprocessedMessagesAsync()).ReturnsAsync(messages);
            _mockOutBoxRepository.Setup(repo => repo.MarkMessageAsProcessedAsync(It.IsAny<Guid>())).Returns(Task.CompletedTask);

            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(500));

            // Act
            await _backgroundService.StartAsync(cts.Token);

            // Assert
            _mockOutBoxRepository.Verify(repo => repo.GetUnprocessedMessagesAsync(), Times.AtLeastOnce);
        }

        [Theory]
        [InlineData("BPSecurityCreate", ProjectSecurityConstants.BPSecurityExchange, RMQConstants.Create)]
        [InlineData("BPSecurityDelete", ProjectSecurityConstants.BPSecurityExchange, RMQConstants.Delete)]
        [InlineData("QCSecurityCreate", ProjectSecurityConstants.QCSecurityExchange, RMQConstants.Create)]
        [InlineData("QCSecurityDelete", ProjectSecurityConstants.QCSecurityExchange, RMQConstants.Delete)]
        public void GetRabbitMQSettings_ShouldReturnCorrectExchangeAndRoutingKey(string typeId, string expectedExchange, string expectedRoutingKey)
        {
            // Act
            var result = _backgroundService.GetType()
                .GetMethod("GetRabbitMQSettings", BindingFlags.NonPublic | BindingFlags.Instance)
                .Invoke(_backgroundService, new object[] { typeId });

            var (exchange, routingKey) = ((string, string))result!;

            // Assert
            exchange.Should().Be(expectedExchange);
            routingKey.Should().Be(expectedRoutingKey);
        }

        [Fact]
        public void GetRabbitMQSettings_ShouldThrowArgumentException_ForUnknownTypeId()
        {
            // Act
            Action act = () => _backgroundService.GetType()
                .GetMethod("GetRabbitMQSettings", BindingFlags.NonPublic | BindingFlags.Instance)
                .Invoke(_backgroundService, new object[] { "UnknownType" });

            // Assert
            act.Should().Throw<TargetInvocationException>()
                .WithInnerException<ArgumentException>()
                .WithMessage("Unknown TypeId: UnknownType");
        }
    }
}
