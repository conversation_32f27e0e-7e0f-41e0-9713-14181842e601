﻿using Builder.ProjectServicesSecurity.API.Domain;
using System.ComponentModel.DataAnnotations;

namespace Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request
{
    public class ProjectUserRequest : IValidatableObject
    {
        public int ProjectTypeId { get; set; }
        public List<int> UserIds { get; set; }
        public List<int> ProjectIds { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();
            if (UserIds.Count <= 0)
            {
                results.Add(new ValidationResult("Must have atleast one user id to proceed"));
            }

            if (ProjectIds.Count <= 0)
            {
                results.Add(new ValidationResult("Must have atleast one project id to proceed"));
            }

            if (ProjectTypeId != 1 && ProjectTypeId != 2)
            {
                results.Add(new ValidationResult("Please provide valid projectTypeId"));
            }

            return results;
        }
    }
}
