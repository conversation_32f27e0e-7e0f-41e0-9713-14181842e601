﻿using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;

namespace Builder.ProjectServicesSecurity.API.Services
{
    public interface IProjectUserService
    {
        Task<Tuple<List<Project>, List<Project>>> AddAsync(ProjectDetails projectDetails);
        Task<List<UserHistory>> GetAsync(int projectId, int projectTypeId);
        Task<List<UserHistory>> GetAsyncList(Project project);
        Task<Tuple<List<int>, List<int>>> DeleteAsync(ProjectDetails projectDetails);
        Task<Tuple<List<Project>, List<Project>>> BulkCopyAsync(BulkCopyProjectUserRequest request, string userName, string countryIds);
    }
}
