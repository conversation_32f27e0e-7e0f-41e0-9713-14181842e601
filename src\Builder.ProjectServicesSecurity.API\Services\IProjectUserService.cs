﻿using Builder.ProjectServicesSecurity.API.Domain;

namespace Builder.ProjectServicesSecurity.API.Services
{
    public interface IProjectUserService
    {
        Task<Tuple<List<Project>, List<Project>>> AddAsync(ProjectDetails projectDetails);
        Task<List<UserHistory>> GetAsync(int projectId, int projectTypeId);
        Task<List<UserHistory>> GetAsyncList(Project project);
        Task<Tuple<List<int>, List<int>>> DeleteAsync(ProjectDetails projectDetails);
    }
}
