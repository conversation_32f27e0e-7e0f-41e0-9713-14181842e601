﻿using System.Reflection.Metadata;
using AutoFixture;
using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Persistence.Repositories;
using Builder.ProjectServicesSecurity.API.Services;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Models;
using Builder.ProjectServicesSecurity.API.Services.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.Services.Constants;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Builder.ProjectServicesSecurity.API.UnitTests.Services
{
    public class ProjectUserServiceTest
    {
        private readonly Mock<IProjectUserRepository> _mockProjectUserRepository;
        private readonly Mock<ILogger<ProjectUserService>> _mockLogger;
        private readonly ProjectUserService _service;
        private readonly Mock<IOutBoxItemRepository> _mockOutBoxItemRepository;
        private readonly Mock<IWebAPIService> _mockWebAPIService;
        private readonly Fixture _fixture;

        public ProjectUserServiceTest()
        {
            _mockProjectUserRepository = new Mock<IProjectUserRepository>();
            _mockLogger = new Mock<ILogger<ProjectUserService>>();
            _mockOutBoxItemRepository = new Mock<IOutBoxItemRepository>();
            _mockWebAPIService = new Mock<IWebAPIService>();

            _service = new ProjectUserService(
                _mockProjectUserRepository.Object,
                _mockLogger.Object,
                _mockOutBoxItemRepository.Object,
                _mockWebAPIService.Object
            );

            _fixture = new Fixture();
        }

        [Fact]
        public async Task AddAsync_ShouldReturnResult_WhenUsersAssignedSuccessfully()
        {
            // Arrange
            var projectTypeId = 1;
            var projects = new List<Project>
            {
                new Project(1, new List<UserHistory> { new UserHistory { UserId = 1 }, new UserHistory { UserId = 2 } })
            };
            var username = "testUser";
            string countryId = null;
            var userIds = new List<int> { 123456 }; 

            var result = new Tuple<List<Project>, List<Project>>(
                new List<Project> { projects[0] },
                new List<Project> { new Project(2, new List<UserHistory> { new UserHistory { UserId = 1 }, new UserHistory { UserId = 2 } }) }
            );

            _mockWebAPIService.Setup(s => s.GetMasterUsers())
                .ReturnsAsync(userIds);

            _mockProjectUserRepository
                .Setup(r => r.AddAsync(It.IsAny<ProjectDetails>(), It.IsAny<List<int>>()))
                .ReturnsAsync(result);

            var projectDetails = new ProjectDetails
            {
                Projects = projects,
                Username = username,
                CountryIds = countryId,
                ProjectTypeId = projectTypeId
            };

            // Act
            var response = await _service.AddAsync(projectDetails);

            // Assert
            response.Should().Be(result);

            _mockOutBoxItemRepository.Verify(
                r => r.SaveMessagesAsync(It.IsAny<ProjectServicesData>(), "BPSecurityCreate"),
                Times.Once
            );

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("ProjectServicesSecurity - AddAsync :: Username:")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()
                ),
                Times.Once
            );
            response.Should().BeEquivalentTo(result);
        }

        [Fact]
        public async Task AddAsync_ShouldThrowHttpRequestException_WhenNoValidProjectsFound()
        {
            // Arrange
            var projectTypeId = 1;
            var projects = new List<Project>
            {
                new Project(1, new List<UserHistory> { new UserHistory { UserId = 1 }, new UserHistory { UserId = 2 } })
            };

            _mockWebAPIService
                .Setup(s => s.GetBaseProject(It.IsAny<BaseProjectCountry>()))
                .ReturnsAsync((List<int>)null);

            var projectDetails = new ProjectDetails
            {
                Projects = projects,
                Username = "testUser",
                CountryIds = "1,2",
                ProjectTypeId = projectTypeId
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<HttpRequestException>(() => _service.AddAsync(projectDetails));
            exception.Message.Should().Be("AddAsync - No Valid Project Found");
        }

        [Fact]
        public async Task GetAsync_ShouldReturnUsers_WhenUsersExist()
        {
            // Arrange
            var projectId = 1;
            var userIds = new List<int> { 3, 4 };
            var userHistories = new List<UserHistory>
            {
                new UserHistory { UserId = 1, CreatedBy = AppConstants.System },
                new UserHistory { UserId = 2, CreatedBy = AppConstants.System }
            };

            var userHistoryResult = new List<UserHistory>
            {
                new UserHistory { UserId = 1, CreatedBy = AppConstants.System },
                new UserHistory { UserId = 2, CreatedBy = AppConstants.System },
                new UserHistory { UserId = 3, CreatedBy = AppConstants.System },
                new UserHistory { UserId = 4, CreatedBy = AppConstants.System }
            };

            _mockWebAPIService.Setup(s => s.GetMasterUsers())
               .ReturnsAsync(userIds);

            _mockProjectUserRepository.Setup(r => r.GetAsync(projectId)).ReturnsAsync(userHistories);

            // Act
            var response = await _service.GetAsync(projectId, (int)ProjectType.BPSecurity);

            // Assert
            response.Should().BeEquivalentTo(userHistoryResult);
        }

        [Fact]
        public async Task DeleteAsync_ShouldReturnDeletedProjects_WhenProjectsDeletedSuccessfully()
        {
            // Arrange
            var project = new Project(1,
                new List<UserHistory>
                {
            new UserHistory { UserId = 123456 }, // Master User
            new UserHistory { UserId = 545687 }  // Non-Master User
                });

            var userIds = new List<int> { 123456 }; // Defining Master Users
            var countryIds = "15,18";
            var username = "testUser";
            int projectTypeId = 1;

            var validProjectIds = new List<int> { 1 }; // Ensure valid project IDs are returned

            _mockWebAPIService.Setup(s => s.GetBaseProject(It.IsAny<BaseProjectCountry>()))
                .ReturnsAsync(validProjectIds);

            _mockWebAPIService.Setup(s => s.GetMasterUsers())
                .ReturnsAsync(userIds); // Ensuring Master Users are defined correctly

            _mockProjectUserRepository.Setup(r => r.DeleteAsync(It.IsAny<Project>(), It.IsAny<string>()))
                .ReturnsAsync(project);

            var projectDetails = new ProjectDetails
            {
                Projects = new List<Project> { project },
                Username = username,
                CountryIds = countryIds,
                ProjectTypeId = projectTypeId
            };

            // Act
            var response = await _service.DeleteAsync(projectDetails);

            // Assert
            response.Item1.Should().NotBeEmpty(); // Master Users List should not be empty
            response.Item1.Should().ContainSingle().Which.Should().Be(123456); // Only 1 master user
            response.Item2.Should().ContainSingle().Which.Should().Be(545687); // Only 1 non-master user

        }


        [Fact]
        public async Task DeleteAsync_ShouldThrowHttpRequestException_WhenNoValidProjectsFound()
        {
            // Arrange
            var project = new Project(1, new List<UserHistory> { new UserHistory { UserId = 1 }, new UserHistory { UserId = 2 } });
            var username = "testUser";

            _mockWebAPIService
                .Setup(s => s.GetBaseProject(It.IsAny<BaseProjectCountry>()))
                .ReturnsAsync((List<int>)null);

            var projectDetails = new ProjectDetails
            {
                Projects = new List<Project> { project },
                Username = username,
                CountryIds = "1,2",
                ProjectTypeId = 1
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<HttpRequestException>(() => _service.DeleteAsync(projectDetails));
            exception.Message.Should().Be("DeleteAsync - No Valid Project Found");
        }

        [Fact]
        public async Task GetAsyncList_ShouldReturnUsers_WhenUsersExist()
        {
            // Arrange
            var project = new Project(1, new List<UserHistory>());
            var userHistories = new List<UserHistory>
            {
                new UserHistory { UserId = 1 },
                new UserHistory { UserId = 2 }
            };

            _mockProjectUserRepository.Setup(repo => repo.GetAsyncList(project)).ReturnsAsync(userHistories);

            // Act
            var result = await _service.GetAsyncList(project);

            // Assert
            result.Should().BeEquivalentTo(userHistories);
        }
        [Fact]
        public async Task GetAsyncList_ShouldThrowArgumentNullException_WhenUsersNotFound()
        {
            // Arrange
            var project = new Project(1, new List<UserHistory>());

            _mockProjectUserRepository.Setup(repo => repo.GetAsyncList(project)).ReturnsAsync((List<UserHistory>)null);

            // Act
            Func<Task> act = async () => await _service.GetAsyncList(project);

            // Assert
            await act.Should().ThrowAsync<ArgumentNullException>()
                .WithMessage("GetAsyncList - users not found (Parameter 'GetAsyncList')");
        }
        [Fact]
        public async Task AddAsync_ShouldThrowHttpRequestException_WhenNoValidQCProjectsFound()
        {
            // Arrange
            var projectTypeId = 2;
            var projects = new List<Project>()
    {
        new Project(1, new List<UserHistory> { new UserHistory { UserId = 1 }, new UserHistory { UserId = 2 } }),
        new Project(2, new List<UserHistory> { new UserHistory { UserId = 3 } })
    };

            var username = "testUser";
            string countryIds = "1,2";

            _mockWebAPIService.Setup(s => s.GetQCProject(It.IsAny<QCProjectCountry>()))
               .ReturnsAsync((List<int>)null);

            var projectDetails = new ProjectDetails
            {
                Projects = projects,
                Username = username,
                CountryIds = countryIds,
                ProjectTypeId = projectTypeId
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<HttpRequestException>(() => _service.AddAsync(projectDetails));
            exception.Message.Should().Be("AddAsync - No Valid Project Found");
        }

        [Fact]
        public async Task DeleteAsync_ShouldThrowHttpRequestException_WhenNoValidBaseProjectsFound()
        {
            // Arrange
            var project = new Project(1, new List<UserHistory> { new UserHistory { UserId = 1 }, new UserHistory { UserId = 2 } });
            var username = "testUser";
            string countryIds = "1,2";
            int projectTypeId = 1;

            _mockWebAPIService.Setup(s => s.GetMasterUsers())
               .ReturnsAsync(new List<int> { 999 });

            _mockWebAPIService.Setup(s => s.GetBaseProject(It.IsAny<BaseProjectCountry>()))
              .ReturnsAsync((List<int>)null);

            var projectDetails = new ProjectDetails
            {
                Projects = new List<Project> { project },
                Username = username,
                CountryIds = countryIds,
                ProjectTypeId = projectTypeId
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<HttpRequestException>(() => _service.DeleteAsync(projectDetails));

            exception.Message.Should().Be("DeleteAsync - No Valid Project Found");
        }
        [Fact]
        public async Task DeleteAsync_ShouldThrowHttpRequestException_WhenNoValidQCProjectsFound()
        {
            // Arrange
            var project = new Project(1, new List<UserHistory> { new UserHistory { UserId = 1 }, new UserHistory { UserId = 2 } });
            var username = "testUser";
            string countryIds = "1,2";
            int projectTypeId = 2;

            _mockWebAPIService.Setup(s => s.GetMasterUsers())
               .ReturnsAsync(new List<int> { 999 });

            _mockWebAPIService.Setup(s => s.GetQCProject(It.IsAny<QCProjectCountry>()))
              .ReturnsAsync((List<int>)null);

            var projectDetails = new ProjectDetails
            {
                Projects = new List<Project> { project },
                Username = username,
                CountryIds = countryIds,
                ProjectTypeId = projectTypeId
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<HttpRequestException>(() => _service.DeleteAsync(projectDetails));
            exception.Message.Should().Be("DeleteAsync - No Valid Project Found");
        }
        [Fact]
        public async Task DeleteAsync_ShouldContainValuesInOneList_WhenOnlyMasterUserFound()
        {
            // Arrange
            var project = new Project(1,
                new List<UserHistory>
                {
            new UserHistory { UserId = 123456 },
            new UserHistory { UserId = 545687 },
            new UserHistory { UserId = 987654 }
                });

            var userIds = new List<int>() { 123456, 545687, 987654 };
            var countryIds = "15,18";
            var username = "testUser";
            int projectTypeId = 1;

            _mockWebAPIService.Setup(s => s.GetBaseProject(It.IsAny<BaseProjectCountry>()))
              .ReturnsAsync(new List<int> { 1 });

            _mockWebAPIService.Setup(s => s.GetMasterUsers())
              .ReturnsAsync(userIds);

            _mockProjectUserRepository.Setup(r => r.DeleteAsync(project, username)).ReturnsAsync(project);

            var projectDetails = new ProjectDetails
            {
                Projects = new List<Project> { project },
                Username = username,
                CountryIds = countryIds,
                ProjectTypeId = projectTypeId
            };

            // Act & Assert
            var response = await _service.DeleteAsync(projectDetails);

            response.Item1.Should().NotBeEmpty();
            response.Item2.Should().BeEmpty();
            userIds.Should().Contain(response.Item1);
        }
        [Fact]
        public async Task DeleteAsync_ShouldContainValuesInBothLists_WhenSomeMasterUserFound()
        {
            // Arrange
            var project = new Project(1,
                new List<UserHistory>
                {
            new UserHistory { UserId = 123456 },
            new UserHistory { UserId = 545687 },
            new UserHistory { UserId = 987654 }
                });

            var userIds = new List<int>() { 123456, 112233, 987654 };
            var countryIds = "15,18";
            var username = "testUser";
            int projectTypeId = 1;

            _mockWebAPIService.Setup(s => s.GetBaseProject(It.IsAny<BaseProjectCountry>()))
              .ReturnsAsync(new List<int> { 1 });

            _mockWebAPIService.Setup(s => s.GetMasterUsers())
              .ReturnsAsync(userIds);

            _mockProjectUserRepository.Setup(r => r.DeleteAsync(project, username)).ReturnsAsync(project);

            var projectDetails = new ProjectDetails
            {
                Projects = new List<Project> { project },
                Username = username,
                CountryIds = countryIds,
                ProjectTypeId = projectTypeId
            };

            // Act & Assert
            var response = await _service.DeleteAsync(projectDetails);

            response.Item1.Should().NotBeEmpty();
            response.Item2.Should().NotBeEmpty();
            response.Item2.Should().ContainSingle();
            userIds.Should().Contain(response.Item1);
        }

    }
}
