﻿using System;
using System.Configuration;
using System.Text.Json.Serialization;
using BootstrapAPI;
using BootstrapAPI.Core;
using BootstrapAPI.Core.Db;
using BootstrapAPI.Tracing;
using Builder.ProjectServicesSecurity.Services;
using Builder.ProjectServicesSecurity.API;
using Builder.ProjectServicesSecurity.API.Persistence;
using Builder.ProjectServicesSecurity.API.Services;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Configuration;
using Builder.ProjectServicesSecurity.API.StartupExtensions;
using Microsoft.EntityFrameworkCore;
using OpenTelemetry.Trace;
using Builder.ProjectServicesSecurity.API.Infrastructure.WebServiceClient;
using Builder.ProjectServicesSecurity.API.Persistence.Repositories;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Infrastructure;


var builder = WebApplication.CreateBuilder(args);
builder.SetupBootstrapCore();

InstrumentationFactory.Add(tracerProviderBuilder =>
    tracerProviderBuilder.AddEntityFrameworkCoreInstrumentation(options => options.SetDbStatementForText = true));


var postgresConnectionString = builder.Configuration["ConnectionStrings:PostgreSQL:DefaultConnection"].ToString() +
                                  builder.Configuration["ConnectionStrings:PostgreSQL:Password"].ToString();

builder.Services.AddHostedService<OutBoxBackgroundService>();
builder.Services.AddHostedService<ConsumerBackgroundService>();

builder.Services.AddDbContext<PostgreSqlDbContext>(options =>
    options.UseNpgsql(postgresConnectionString));

builder.Services.AddControllersWithViews(options =>
{
    options.Conventions.Add(new LowercaseControllerModelConvention());
});
builder.Services.AddBootstrapApi<PostgreSqlDbContext>(builder.Configuration);

builder.Services.AddControllers()
    .AddJsonOptions(options =>
{
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
});

builder.Services.AddServices(builder.Configuration);
builder.Services.AddRepositories(builder.Configuration);

builder.Services.Configure<RabbitMQSettings>(builder.Configuration.GetSection("ConnectionStrings:RabbitMQ"));
builder.Services.Configure<WebServiceClientOptions>(builder.Configuration.GetSection("WebServiceClient"));

var webServiceClientOptions = builder.Configuration.GetSection("WebServiceClient").Get<WebServiceClientOptions>();

builder.Services.AddHttpClient("ProjectServicesAPI", client =>
{
    client.BaseAddress = new Uri(webServiceClientOptions.BaseAddress.ProjectServicesAPI);
});

builder.Services.AddHttpClient("ProjectServicesSecurityAPI", client =>
{
    client.BaseAddress = new Uri(webServiceClientOptions.BaseAddress.ProjectServicesSecurityAPI);
});

builder.Services.AddHttpClient("UserRoleAssignmentAPI", client =>
{
    client.BaseAddress = new Uri(webServiceClientOptions.BaseAddress.UserRoleAssignmentAPI);
});

builder.Services.Configure<TokenSettings>(builder.Configuration.GetSection("TokenSettings"));

var app = builder.Build();

AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

app.UseBootstrapApi();

app.Run();

public partial class Program
{
    protected Program()
    {

    }
}
