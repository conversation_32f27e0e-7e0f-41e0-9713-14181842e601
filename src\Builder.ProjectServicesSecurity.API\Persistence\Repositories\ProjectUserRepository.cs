﻿using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Persistence.Entities;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Models;
using Builder.ProjectServicesSecurity.Services.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Builder.ProjectServicesSecurity.API.Persistence.Repositories
{
    public class ProjectUserRepository : IProjectUserRepository
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<ProjectUserRepository> _logger;
        private readonly PostgreSqlDbContext _postdbContext;

        public ProjectUserRepository(IServiceScopeFactory serviceScopeFactory, PostgreSqlDbContext postgreSqlDbContext, ILogger<ProjectUserRepository> logger)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _postdbContext = postgreSqlDbContext;
            _logger = logger;
        }

        public async Task<Tuple<List<Project>, List<Project>>> AddAsync(ProjectDetails projectDetails, List<int> masterUserIds)
        {
            _logger.LogDebug("Entered in AddAsync Repository method");

            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            List<Project> successfulProjects = new List<Project>();
            List<Project> failedProjects = new List<Project>();

            var projectUserData = await dbContext.ProjectUsers
                .Where(p => projectDetails.Projects.Select(pr => pr.ProjectId).Contains(p.ProjectId)
                 && p.ProjectTypeId == projectDetails.ProjectTypeId)
                .ToListAsync();

            foreach (var project in projectDetails.Projects)
            {
                var existingProjectUsers = projectUserData
                    .Where(p => p.ProjectId == project.ProjectId && !p.Deleted)
                    .Select(p => p.UserId)
                    .Concat(masterUserIds)
                    .Distinct()
                    .ToList();

                var newProjectUsers = project.Users.Select(u => u.UserId).Except(existingProjectUsers).ToList();
                var duplicateProjectUsers = project.Users.Select(u => u.UserId).Intersect(existingProjectUsers).ToList();

                if (newProjectUsers.Count > 0)
                {
                    var userHistory = newProjectUsers.Select(userId => new UserHistory
                    {
                        UserId = userId
                    }).ToList();

                    successfulProjects.Add(new Project(project.ProjectId, userHistory));

                    foreach (var userId in newProjectUsers)
                    {
                        var deletedProjectUser = projectUserData
                            .FirstOrDefault(i => i.UserId == userId && i.ProjectId == project.ProjectId && i.Deleted);

                        if (deletedProjectUser != null)
                        {
                            dbContext.ProjectUsers.Remove(deletedProjectUser);
                        }

                        var entity = new ProjectUserEntity
                        {
                            ProjectId = project.ProjectId,
                            UserId = userId,
                            CreatedBy = projectDetails.Username,
                            CreatedWhen = DateTimeOffset.UtcNow,
                            UpdatedBy = projectDetails.Username,
                            UpdatedWhen = DateTimeOffset.UtcNow,
                            Deleted = false,
                            ProjectTypeId = projectDetails.ProjectTypeId
                        };
                        dbContext.ProjectUsers.Add(entity);
                    }
                    await dbContext.SaveChangesAsync();
                }

                if (duplicateProjectUsers.Count > 0)
                {
                    var userHistory = duplicateProjectUsers.Select(userId => new UserHistory
                    {
                        UserId = userId
                    }).ToList();

                    failedProjects.Add(new Project(project.ProjectId, userHistory));
                }
            }
            return new Tuple<List<Project>, List<Project>>(successfulProjects, failedProjects);
        }


        public async Task<Project> DeleteAsync(Project project, string userName)
        {
            _logger.LogDebug("Entered in DeleteAsync Repository method");   
            var projectUsers = await _postdbContext.ProjectUsers
                .Where(pu => pu.ProjectId == project.ProjectId && !pu.Deleted)
                .ToListAsync();

            foreach (var user in project.Users)
            {
                var projectUser = projectUsers.FirstOrDefault(pu => pu.UserId == user.UserId);

                if (projectUser == null)
                {
                    return null;
                }
                projectUser.DeletedWhen = DateTimeOffset.UtcNow;
                projectUser.DeletedBy = userName;
                projectUser.Deleted = true;
            }

            await _postdbContext.SaveChangesAsync();
            return project;
        }

        public async Task<List<UserHistory>> GetAsync(int qcprojectId)
        {
            _logger.LogDebug("Entered in GetAsync Repository method");

            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var projectDetails = await dbContext.ProjectUsers
                                        .Where(p => p.ProjectId == qcprojectId && !p.Deleted)
                                        .OrderBy(p => p.UserId)
                                        .ToListAsync();

            var projectTypeId = projectDetails
                .Select(s => s.ProjectTypeId).FirstOrDefault();

            return projectDetails.Select(p => new UserHistory
            {
                UserId = p.UserId,
                CreatedBy = p.CreatedBy,
                CreatedWhen = p.CreatedWhen
            }).ToList();
        }

        public async Task<List<UserHistory>> GetAsyncList(Project project)
        {
            _logger.LogDebug("Entered in GetAsyncList Repository method");

            var result = await (from pu in _postdbContext.ProjectUsers
                                where pu.ProjectId == project.ProjectId
                                && project.Users.Select(c => c.UserId).Contains(pu.UserId)
                                orderby pu.UserId
                                select new
                                {
                                    pu.UserId,
                                    pu.CreatedBy,
                                    pu.CreatedWhen,
                                    pu.DeletedBy,
                                    pu.DeletedWhen
                                })
                                .ToListAsync();

            return result.Select(p => new UserHistory
            {
                UserId = p.UserId,
                CreatedBy = p.CreatedBy,
                CreatedWhen = p.CreatedWhen,
                DeletedBy = p.DeletedBy,
                DeletedOn = p.DeletedWhen

            }).ToList();
        }

        public async Task<List<UserHistory>> GetUsersByProjectIdsAsync(List<int> projectIds)
        {
            _logger.LogDebug("Entered in GetUsersByProjectIdsAsync Repository method");

            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var projectUsers = await dbContext.ProjectUsers
                                        .Where(p => projectIds.Contains(p.ProjectId) && !p.Deleted)
                                        .OrderBy(p => p.ProjectId)
                                        .ThenBy(p => p.UserId)
                                        .ToListAsync();

            return projectUsers.Select(p => new UserHistory
            {
                UserId = p.UserId,
                CreatedBy = p.CreatedBy,
                CreatedWhen = p.CreatedWhen,
                ProjectTypeId = p.ProjectId // Temporarily use ProjectTypeId to store ProjectId for grouping
            }).ToList();
        }

        public async Task<Tuple<List<Project>, List<Project>>> BulkAddAsync(List<Project> projects, string userName)
        {
            _logger.LogDebug("Entered in BulkAddAsync Repository method");

            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var successfulProjects = new List<Project>();
            var failedProjects = new List<Project>();

            // Get all project IDs and user IDs for bulk operations
            var allProjectIds = projects.Select(p => p.ProjectId).ToList();
            var allUserIds = projects.SelectMany(p => p.Users.Select(u => u.UserId)).Distinct().ToList();

            // Get existing user-project combinations in a single query
            var existingUserProjects = await dbContext.ProjectUsers
                .Where(pu => allProjectIds.Contains(pu.ProjectId) &&
                            allUserIds.Contains(pu.UserId) &&
                            !pu.Deleted)
                .Select(pu => new { pu.ProjectId, pu.UserId })
                .ToListAsync();

            var existingCombinations = existingUserProjects
                .Select(ep => $"{ep.ProjectId}-{ep.UserId}")
                .ToHashSet();

            // Prepare bulk insert data
            var newProjectUsers = new List<ProjectUserEntity>();
            var currentTime = DateTimeOffset.UtcNow;

            foreach (var project in projects)
            {
                var newUsers = new List<UserHistory>();
                var duplicateUsers = new List<UserHistory>();

                foreach (var user in project.Users)
                {
                    var combination = $"{project.ProjectId}-{user.UserId}";

                    if (!existingCombinations.Contains(combination))
                    {
                        // New user - add to bulk insert
                        newProjectUsers.Add(new ProjectUserEntity
                        {
                            ProjectId = project.ProjectId,
                            UserId = user.UserId,
                            CreatedBy = userName,
                            CreatedWhen = currentTime,
                            UpdatedBy = userName,
                            UpdatedWhen = currentTime,
                            Deleted = false,
                            ProjectTypeId = (int)Domain.ProjectType.BPSecurity
                        });
                        newUsers.Add(user);
                    }
                    else
                    {
                        // Duplicate user
                        duplicateUsers.Add(user);
                    }
                }

                // Categorize project based on results
                if (newUsers.Any())
                {
                    successfulProjects.Add(new Project(project.ProjectId, newUsers));
                }

                if (duplicateUsers.Any())
                {
                    failedProjects.Add(new Project(project.ProjectId, duplicateUsers));
                }
            }

            // Bulk insert all new users in a single database operation
            if (newProjectUsers.Any())
            {
                await dbContext.ProjectUsers.AddRangeAsync(newProjectUsers);
                await dbContext.SaveChangesAsync();
            }

            return new Tuple<List<Project>, List<Project>>(successfulProjects, failedProjects);
        }

        public async Task DeleteMasterUsersFromBX(List<int> masterUserIds)
        {
            _logger.LogDebug("Entered in DeleteMasterUsersFromBX method");

            var masterUsers = await _postdbContext.ProjectUsers
                .Where(pu => masterUserIds.Contains(pu.UserId) && pu.ProjectTypeId == (int)Domain.ProjectType.BPSecurity && !pu.Deleted)
                .ToListAsync();


            if (masterUsers.Count > 0)
            {
                foreach (var masterUser in masterUsers)
                {
                    masterUser.DeletedWhen = DateTimeOffset.UtcNow;
                    masterUser.DeletedBy = AppConstants.System;
                    masterUser.Deleted = true;
                }
                await _postdbContext.SaveChangesAsync();
            }
        }
    }
}
