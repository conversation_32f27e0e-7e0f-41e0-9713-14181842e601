﻿using Builder.ProjectServicesSecurity.API.Domain;

namespace Builder.ProjectServicesSecurity.API.Persistence.Repositories
{
    public interface IProjectUserRepository
    {
        Task<Tuple<List<Project>, List<Project>>> AddAsync(ProjectDetails projectDetails, List<int> masterUserIds);
        Task<Project> DeleteAsync(Project project, string userName);
        Task<List<UserHistory>> GetAsync(int qcprojectId);
        Task<List<UserHistory>> GetAsyncList(Project project);
        Task<List<UserHistory>> GetUsersByProjectIdsAsync(List<int> projectIds);
        Task DeleteMasterUsersFromBX(List<int> masterUserIds);
    }
}
